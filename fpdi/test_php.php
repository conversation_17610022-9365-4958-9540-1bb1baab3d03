<?php

	echo "Step 01";
 
	require_once("db_login.php");
	require_once('DB.php');
	require('fpdf/fpdf.php'); 

	// Get Company Information
	//==============================
	
   $connection = DB::connect("mysql://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysql://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 
 
	 
 
	 
class PDF extends FPDF
{

	function PDF($orientation='L',$unit='mm',$format='A4')
	{
		//Call parent constructor
		$this->FPDF($orientation,$unit,$format);
	}


	//Page header
	function Header()
	{
		$this->Ln(10);
		$this->SetFont('Times','',36);
		$this->Cell(10,4,'N Y C',1,1,'L');
 		$this->SetFont('Times','',9);
 
		

		
	}

	//Page footer
	function Footer()
	{
		//Position at 1.5 cm from bottom
		$this->SetY(-15);
		//Arial italic 8
		$this->SetFont('Times','I',9);
		//Page number
		$this->Cell(0,10,'Page '.$this->PageNo().'/{nb}',0,0,'C');
	}
}
 
	//Instanciation of inherited class
	$pdf=new PDF();
	$pdf->AliasNbPages();
	$pdf->AddPage();
	$pdf->SetFont('Arial','B',9);
	
	//+++++++++++++++++++++++++++++++++++++++++++++++++
    
	 
	
		 
	 

	//+++++++++++++++++++++++++++++++++++++++++++++++++
	 
	
	$pdf->Output();
	 
	$connection->disconnect();

	 	
?>
