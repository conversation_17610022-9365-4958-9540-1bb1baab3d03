<?php
/*******************************************************************************
* Software: PDF_ImageAlpha
* Version:  1.4
* Date:     2009-12-28
* Author:   <PERSON><PERSON> 
*
* Requirements: FPDF 1.6
*
* This script allows to use images (PNGs or JPGs) with alpha-channels. 
* The alpha-channel can be either supplied as separate 8-bit PNG ("mask"), 
* or, for PNGs, also an internal alpha-channel can be used. 
* For the latter the GD 2.x extension is required.
*******************************************************************************/ 

//require('../fpdf/fpdf.php');
//require('http://www.ewebstaffing.com/gotham_test/data/fpdi/fpdi.php');
//require('/var/www/gotham_test/data/fpdf/fpdf.php');

	require_once('../fpdi/fpdi.php');
	require_once('../fpdf/fpdf.php');


	echo 'Step 02'; 	

?>  