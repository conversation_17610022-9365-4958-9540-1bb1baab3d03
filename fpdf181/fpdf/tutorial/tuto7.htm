<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>Adding new fonts and encoding support</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
<style type="text/css">
table {border-collapse:collapse; border-style:solid; border-width:2px; border-color:#A0A0A0 #000000 #000000 #A0A0A0}
table {margin:1.4em 0 1.4em 1em}
th {background-color:#E0EBFF; color:#900000; text-align:left}
th, td {border:1px solid #808080; padding:2px 10px}
tr.alt0 {background-color:#FFFFEE}
tr.alt1 {background-color:#FFFFE0}
</style>
</head>
<body>
<h1>Adding new fonts and encoding support</h1>
This tutorial explains how to use TrueType or Type1 fonts so that you are not limited to the standard
fonts any more. The other interest is that you can choose the font encoding, which allows you to
use other languages than the Western ones (the standard fonts having too few available characters).
<br>
<br>
There are two ways to use a new font: embedding it in the PDF or not. When a font is not
embedded, it is searched in the system. The advantage is that the PDF file is lighter; on the other
hand, if it is not available, a substitution font is used. So it is preferable to ensure that the
needed font is installed on the client systems. If the file is to be viewed by a large audience,
it is recommended to embed.
<br>
<br>
Adding a new font requires three steps for TrueTypes:
<ul>
<li>Generation of the metric file (.afm)</li>
<li>Generation of the font definition file (.php)</li>
<li>Declaration of the font in the script</li>
</ul>
For Type1, the first one is theoretically not necessary because the AFM file is usually shipped
with the font. In case you have only a metric file in PFM format, use the convertor available
<a href="http://www.fpdf.org/fr/dl.php?id=34">here</a>.
<h2>Generation of the metric file</h2>
The first step for a TrueType consists in generating the AFM file. A utility exists to do this
task: <a href="http://ttf2pt1.sourceforge.net" target="_blank">ttf2pt1</a>. The Windows binary
is available <a href="http://www.fpdf.org/fr/dl.php?id=22">here</a>. The command line to use is
the following:
<br>
<br>
<kbd>ttf2pt1 -a font.ttf font</kbd>
<br>
<br>
For example, for Comic Sans MS Regular:
<br>
<br>
<kbd>ttf2pt1 -a c:\windows\fonts\comic.ttf comic</kbd>
<br>
<br>
Two files are created; the one we are interested in is comic.afm.
<h2>Generation of the font definition file</h2>
The second step consists in generating a PHP file containing all the information needed by FPDF;
in addition, the font file is compressed. To do this, a helper script is provided in the font/makefont/
directory of the package: makefont.php. It contains the following function:
<br>
<br>
<code>MakeFont(<b>string</b> fontfile, <b>string</b> afmfile [, <b>string</b> enc [, <b>array</b> patch [, <b>string</b> type]]])</code>
<dl class="param" style="margin-bottom:2em">
<dt><code>fontfile</code></dt>
<dd>
<p>Path to the .ttf or .pfb file.</p>
</dd>
<dt><code>afmfile</code></dt>
<dd>
<p>Path to the .afm file.</p>
</dd>
<dt><code>enc</code></dt>
<dd>
<p>Name of the encoding to use. Default value: <code>cp1252</code>.</p>
</dd>
<dt><code>patch</code></dt>
<dd>
<p>Optional modification of the encoding. Empty by default.</p>
</dd>
<dt><code>type</code></dt>
<dd>
<p>Type of the font (<code>TrueType</code> or <code>Type1</code>). Default value: <code>TrueType</code>.</p>
</dd>
</dl>
The first parameter is the name of the font file. The extension must be either .ttf or .pfb and
determines the font type. If you own a Type1 font in ASCII format (.pfa), you can convert it to
binary format with <a href="http://www.lcdf.org/~eddietwo/type/#t1utils" target="_blank">t1utils</a>.
<br>
If you don't want to embed the font, pass an empty string. In this case, type is given by the
<code>type</code> parameter.
<br>
Note: in the case of a font with the same name as a standard one, for instance arial.ttf, it is
recommended to embed. If you don't, some versions of Acrobat will use their own fonts.
<br>
<br>
The AFM file is the one previously generated.
<br>
<br>
The encoding defines the association between a code (from 0 to 255) and a character. The first
128 are fixed and correspond to ASCII; the following are variable. The encodings are stored in
.map files. Those available are:
<ul>
<li>cp1250 (Central Europe)</li>
<li>cp1251 (Cyrillic)</li>
<li>cp1252 (Western Europe)</li>
<li>cp1253 (Greek)</li>
<li>cp1254 (Turkish)</li>
<li>cp1255 (Hebrew)</li>
<li>cp1257 (Baltic)</li>
<li>cp1258 (Vietnamese)</li>
<li>cp874 (Thai)</li>
<li>ISO-8859-1 (Western Europe)</li>
<li>ISO-8859-2 (Central Europe)</li>
<li>ISO-8859-4 (Baltic)</li>
<li>ISO-8859-5 (Cyrillic)</li>
<li>ISO-8859-7 (Greek)</li>
<li>ISO-8859-9 (Turkish)</li>
<li>ISO-8859-11 (Thai)</li>
<li>ISO-8859-15 (Western Europe)</li>
<li>ISO-8859-16 (Central Europe)</li>
<li>KOI8-R (Russian)</li>
<li>KOI8-U (Ukrainian)</li>
</ul>
Of course, the font must contain the characters corresponding to the chosen encoding.
<br>
In the particular case of a symbolic font (that is to say which does not contain letters, such
as Symbol or ZapfDingbats), pass an empty string.
<br>
The encodings which begin with cp are those used by Windows; Linux systems usually use ISO.
<br>
Remark: the standard fonts use cp1252.
<br>
<br>
The fourth parameter gives the possibility to alter the encoding. Sometimes you may want to add
some characters. For instance, ISO-8859-1 does not contain the euro symbol. To add it at position
164, pass <code>array(164=>'Euro')</code>.
<br>
<br>
The last parameter is used to give the type of the font in case it is not embedded (that is to
say the first parameter is empty).
<br>
<br>
After you have called the function (create a new file for this and include makefont.php, or
simply add the call directly inside), a .php file is created, with the same name as the .afm one.
You may rename it if you wish. If the case of embedding, the font file is compressed and gives a
second file with .z as extension (except if the compression function is not available, it
requires zlib). You may rename it too, but in this case you have to alter the variable <code>$file</code>
in the .php file accordingly.
<br>
<br>
Example:
<div class="source">
<pre><code>MakeFont<span class="kw">(</span><span class="str">'c:\\windows\\fonts\\comic.ttf'</span><span class="kw">,</span><span class="str">'comic.afm'</span><span class="kw">,</span><span class="str">'cp1252'</span><span class="kw">);
</span></code></pre>
</div>
which gives the files comic.php and comic.z.
<br>
<br>
Then you have to copy the generated file(s) to the font directory. If the font file
could not be compressed, copy the .ttf or .pfb instead of the .z.
<br>
<br>
Remark: for TTF fonts, you can generate the files online <a href="http://fpdf.fruit-lab.de" target="_blank">here</a>
instead of doing it manually.
<h2>Declaration of the font in the script</h2>
The last step is the most simple. You just need to call the <a href='../doc/addfont.htm'>AddFont()</a> method. For instance:
<div class="source">
<pre><code>$pdf<span class="kw">-&gt;</span>AddFont<span class="kw">(</span><span class="str">'Comic'</span><span class="kw">,</span><span class="str">''</span><span class="kw">,</span><span class="str">'comic.php'</span><span class="kw">);
</span></code></pre>
</div>
or simply:
<div class="source">
<pre><code>$pdf<span class="kw">-&gt;</span>AddFont<span class="kw">(</span><span class="str">'Comic'</span><span class="kw">);
</span></code></pre>
</div>
And the font is now available (in regular and underlined styles), usable like the others. If we
had worked with Comic Sans MS Bold (comicbd.ttf), we would have put:
<div class="source">
<pre><code>$pdf<span class="kw">-&gt;</span>AddFont<span class="kw">(</span><span class="str">'Comic'</span><span class="kw">,</span><span class="str">'B'</span><span class="kw">,</span><span class="str">'comicbd.php'</span><span class="kw">);
</span></code></pre>
</div>
<h2>Example</h2>
Let's now see a small complete example. The font used is Calligrapher, available at
<a href="http://www.abstractfonts.com/fonts/" target="_blank">www.abstractfonts.com</a> (a site
offering numerous free TrueType fonts). The first step is the generation of the AFM file:
<br>
<br>
<kbd>ttf2pt1 -a calligra.ttf calligra</kbd>
<br>
<br>
which gives calligra.afm (and calligra.t1a that we can delete). Then we generate the definition
file:
<div class="source">
<pre><code>&lt;?php
<span class="kw">require(</span><span class="str">'font/makefont/makefont.php'</span><span class="kw">);

</span>MakeFont<span class="kw">(</span><span class="str">'calligra.ttf'</span><span class="kw">,</span><span class="str">'calligra.afm'</span><span class="kw">);
</span>?&gt;</code></pre>
</div>
The function call gives the following report:
<br>
<br>
<b>Warning:</b> character Euro is missing<br>
<b>Warning:</b> character Zcaron is missing<br>
<b>Warning:</b> character zcaron is missing<br>
<b>Warning:</b> character eth is missing<br>
Font file compressed (calligra.z)<br>
Font definition file generated (calligra.php)<br>
<br>
The euro character is not present in the font (it is too old). Three other characters are missing
too, but we are not interested in them anyway.
<br>
We can now copy the two files to the font directory and write the script:
<div class="source">
<pre><code>&lt;?php
<span class="kw">require(</span><span class="str">'fpdf.php'</span><span class="kw">);

</span>$pdf<span class="kw">=new </span>FPDF<span class="kw">();
</span>$pdf<span class="kw">-&gt;</span>AddFont<span class="kw">(</span><span class="str">'Calligrapher'</span><span class="kw">,</span><span class="str">''</span><span class="kw">,</span><span class="str">'calligra.php'</span><span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>AddPage<span class="kw">();
</span>$pdf<span class="kw">-&gt;</span>SetFont<span class="kw">(</span><span class="str">'Calligrapher'</span><span class="kw">,</span><span class="str">''</span><span class="kw">,</span>35<span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>Cell<span class="kw">(</span>0<span class="kw">,</span>10<span class="kw">,</span><span class="str">'Enjoy new fonts with FPDF!'</span><span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>Output<span class="kw">();
</span>?&gt;</code></pre>
</div>
<p class='demo'><a href='tuto7.php' target='_blank' class='demo'>[Demo]</a></p>
<h2>About the euro symbol</h2>
The euro character is not present in all encodings, and is not always placed at the same position:
<table>
<tr><th>Encoding</th><th>Position</th></tr>
<tr class="alt0"><td>cp1250</td><td>128</td></tr>
<tr class="alt1"><td>cp1251</td><td>136</td></tr>
<tr class="alt0"><td>cp1252</td><td>128</td></tr>
<tr class="alt1"><td>cp1253</td><td>128</td></tr>
<tr class="alt0"><td>cp1254</td><td>128</td></tr>
<tr class="alt1"><td>cp1255</td><td>128</td></tr>
<tr class="alt0"><td>cp1257</td><td>128</td></tr>
<tr class="alt1"><td>cp1258</td><td>128</td></tr>
<tr class="alt0"><td>cp874</td><td>128</td></tr>
<tr class="alt1"><td>ISO-8859-1</td><td>absent</td></tr>
<tr class="alt0"><td>ISO-8859-2</td><td>absent</td></tr>
<tr class="alt1"><td>ISO-8859-4</td><td>absent</td></tr>
<tr class="alt0"><td>ISO-8859-5</td><td>absent</td></tr>
<tr class="alt1"><td>ISO-8859-7</td><td>absent</td></tr>
<tr class="alt0"><td>ISO-8859-9</td><td>absent</td></tr>
<tr class="alt1"><td>ISO-8859-11</td><td>absent</td></tr>
<tr class="alt0"><td>ISO-8859-15</td><td>164</td></tr>
<tr class="alt1"><td>ISO-8859-16</td><td>164</td></tr>
<tr class="alt0"><td>KOI8-R</td><td>absent</td></tr>
<tr class="alt1"><td>KOI8-U</td><td>absent</td></tr>
</table>
ISO-8859-1 is widespread but does not include the euro sign. If you need it, the simplest thing
to do is using cp1252 or ISO-8859-15 instead, which are nearly identical but contain the precious
symbol.
<br>
As for ISO-8859-2, it is possible to use ISO-8859-16 instead, but it contains many differences.
It is therefore simpler to patch the encoding to add the symbol to it, as explained above. The
same is true for the other encodings.
<h2>Font synthesis under Windows</h2>
When a TrueType font is not available in a given style, Windows is able to synthesize it from the
regular version. For instance, there is no Comic Sans MS Italic, but it can be built from Comic
Sans MS Regular. This feature can be used in a PDF file, but unfortunately requires that the
regular font be present in the system (you must not embed it). Here is how to do it:
<ul>
<li>Generate the definition file for the regular font without embedding (you may rename it to
reflect the desired style)</li>
<li>Open it and append to the variable <code>$name</code> a comma followed by the desired style
(<code>Italic</code>, <code>Bold</code> or <code>BoldItalic</code>)</li>
</ul>
For instance, for the file comici.php:
<br>
<br>
<code>$name='ComicSansMS,Italic';</code>
<br>
<br>
It can then be used normally:
<div class="source">
<pre><code>$pdf<span class="kw">-&gt;</span>AddFont<span class="kw">(</span><span class="str">'Comic'</span><span class="kw">,</span><span class="str">'I'</span><span class="kw">,</span><span class="str">'comici.php'</span><span class="kw">);
</span></code></pre>
</div>
<h2>Reducing the size of TrueType fonts</h2>
Font files are often quite voluminous (more than 100, even 200KB); this is due to the fact that
they contain the characters corresponding to many encodings. zlib compression reduces them but
they remain fairly big. A technique exists to reduce them further. It consists in converting the
font to the Type1 format with ttf2pt1 by specifying the encoding you are interested in; all other
characters will be discarded.
<br>
For instance, the arial.ttf font shipped with Windows 98 is 267KB (it contains 1296 characters).
After compression it gives 147. Let's convert it to Type1 by keeping only cp1250 characters:
<br>
<br>
<kbd>ttf2pt1 -b -L cp1250.map c:\windows\fonts\arial.ttf arial</kbd>
<br>
<br>
The .map files are located in the font/makefont/ directory of the package. The command produces
arial.pfb and arial.afm. The arial.pfb file is only 35KB, and 30KB after compression.
<br>
<br>
It is possible to go even further. If you are interested only by a subset of the encoding (you
probably don't need all 217 characters), you can open the .map file and remove the lines you are
not interested in. This will reduce the file size accordingly.
</body>
</html>
