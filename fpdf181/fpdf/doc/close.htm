<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>Close</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>Close</h1>
<code>Close()</code>
<h2>Description</h2>
Terminates the PDF document. It is not necessary to call this method explicitly because Output()
does it automatically.
<br>
If the document contains no page, AddPage() is called to prevent from getting an invalid document.
<h2>See also</h2>
<a href="output.htm">Output()</a>.
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
